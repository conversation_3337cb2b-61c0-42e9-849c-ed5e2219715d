<script setup>
import { nextTick } from 'vue';
import { useModal } from 'vue-final-modal';
import BiCreateWidget from '~/bi/components/bi-create-widget.vue';

const create_widget = useModal({
  component: BiCreateWidget,
  attrs: {
    onClose() {
      create_widget.close();
    },
  },
});

const $t = inject('$t');

const state = reactive({
  is_editing_grid: false,
  layout: [
    { x: 0, y: 0, w: 10, h: 15, i: '0', widget_id: 'first_widget' },
    { x: 0, y: 0, w: 8, h: 10, i: '1', widget_id: 'second_widget' },
  ],
});

const dashboard_action_items = computed(() => {
  return [
    { label: $t('Edit'), on_click: () => (state.is_editing_grid = true) },
    { label: $t('Export PDF'), on_click: () => printDashboard() },
    { label: $t('Archive'), on_click: () => {} },
    { label: $t('Delete'), on_click: () => {} },
  ];
});

const bi_grid = ref(null);

function createWidget() {
  create_widget.open();
}

async function onSave() {
  state.layout = bi_grid.value.getCurrentLayout();
  await nextTick();
  state.is_editing_grid = false;
}

async function printDashboard() {
  logger.log('Print dashboard');
}
</script>

<template>
  <div class="flex h-full">
    <div class="w-1/5 p-4">
      BI Dashboard (Details and Metadata)
    </div>
    <!-- Vertical line -->
    <div class="w-full h-[calc(100vh-65px)] border-l">
      <div class="px-4 pt-3 flex items-center gap-3">
        <HawkTabs
          :tabs="[{ uid: '1', label: 'Overview' }, { uid: '2', label: 'Portfolio performance' }, { uid: '3', label: 'Website analytics' }]"
          active_item="1"
          container_class="border-transparent"
        />
        <HawkButton
          icon
          type="text"
          class="-mt-3"
        >
          <IconHawkPlus />
        </HawkButton>
      </div>
      <hr>
      <div class="px-4 py-2 bg-gray-50 flex justify-between items-center">
        <div class="flex items-center">
          Filters (Coming soon™)
          <HawkButton v-if="state.is_editing_grid" icon type="text" class="hover:!bg-gray-200">
            <IconHawkSettingsOne />
          </HawkButton>
        </div>
        <div v-if="state.is_editing_grid" class="flex items-center gap-1">
          <HawkButton type="text" @click="createWidget">
            <IconHawkPlus class="text-primary-700" />
            <span class="whitespace-nowrap text-primary-700">
              Add widget
            </span>
          </HawkButton>
          <HawkButton @click="onSave">
            <IconHawkRocketTwo />
            Publish
          </HawkButton>
        </div>
        <div v-else class="flex items-center gap-3">
          <HawkMenu :items="dashboard_action_items">
            <template #trigger>
              <HawkButton icon type="text" class="hover:!bg-gray-200">
                <IconHawkSettingsOne />
              </HawkButton>
            </template>
          </HawkMenu>
        </div>
      </div>
      <hr>
      <div class="h-full w-full">
        <BiGrid
          ref="bi_grid"
          :layout="state.layout"
          :is-editing="state.is_editing_grid"
        />
      </div>
    </div>
  </div>
</template>
