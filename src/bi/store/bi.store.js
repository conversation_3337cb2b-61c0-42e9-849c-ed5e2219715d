import { acceptHMRUpdate, defineStore } from 'pinia';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';

const bi_query_builder = useBIQueryBuilder();

export const useBiStore = defineStore('bi', {
  state: () => ({
    all_tables: [],
    selected_table: null,
    selected_database: 'grs_corvera',
    stages: [],
    config_change_detection_counter: 1,
    data: [],
    table_metadata: {},
    selected_stage_index: null,
    are_chart_builder_fields_filled: false,
    chart_builder_config: {},
    is_table_dirty: false,
    table_preview_config: {
      columns_map: {},
      conditional_formatting_rules: [],
    },
    pivot_table_preview_config: {
      rows: [],
      columns: [],
      values: [],
      show_row_totals: false,
      show_column_totals: false,
      show_grand_totals: false,
      conditional_formatting_rules: [],
    },
  }),
  getters: {
    alias_to_column_mapping() {
      if (!this.stages.length && !this.stages[0].value.columns.length)
        return {};
      const column_mapping = {};
      this.stages.forEach(stage => stage.value.columns.forEach((column) => {
        column_mapping[column.alias] = column;
      }));
      return column_mapping;
    },
    stage_config() {
      if (this.stages[0]?.value?.columns?.length === 0)
        return;
      const getBin = field => ({ dateBin: field.type === 'date' ? field.agg : undefined, binCount: field.type === 'integer' ? field.agg : undefined });
      const getFunction = field => ({ agg: field.is_bin ? undefined : field.agg, ...(field.is_bin ? getBin(field) : {}) });
      const getField = (field, selected_table_name) => ({ field: field.table_name ? (`${field.table_name}→${field.label}`) : (`${selected_table_name}${field.label}`), ...getFunction(field), alias: field.alias });
      const getExpression = field => ({ expr: field.expression, type: field.aggregation_type === 'aggregations' ? 'aggregation' : 'column', alias: field.alias });
      const getJoin = (stage) => {
        if (stage.tables.length > 1) {
          const joins = stage.tables.filter((t, index) => index > 0).map((table) => {
            return {
              table: { name: table.label, alias: table.alias },
              type: table.type,
              on: { left: table.on.left, right: `${table.on.right}` },
            };
          });
          return joins;
        }
        else {
          return undefined;
        }
      };

      const getTableConfig = stage => ({ table: stage.selected_table?.label, orderBy: stage.value.orderBy.map(column => ({ column: column.alias, direction: column.direction })), limit: stage.value.limit ? stage.value.limit : undefined, columns: stage.value.columns.map(field => field.expression ? getExpression(field) : getField(field, stage.selected_table?.label ? `${stage.selected_table.label}→` : '')), joins: getJoin(stage) });
      const config = this.stages.filter(stage => stage.value?.columns?.length > 0).map(stage => getTableConfig(stage));
      return config;
    },
    chart_builder_data_types() {
      return Object.keys(this.alias_to_column_mapping).reduce((acc, key) => {
        let type = this.alias_to_column_mapping[key].type;
        switch (this.alias_to_column_mapping[key].type) {
          case 'string':
          case 'text':
          case 'text_array':
            type = 'text';
            break;
          case 'numeric':
          case 'integer':
          case 'decimal':
          case 'float':
            type = 'numeric';
            break;
          case 'date':
          case 'time':
          case 'timestamp':
            type = 'date';
            break;
          case 'boolean':
            type = 'boolean';
            break;
        }
        acc[key] = type;
        return acc;
      }, {});
    },
  },
  actions: {
    async resetStore() {
      this.$reset();
    },
    async getTables() {
      const response = await this.$services.bi_schema.table({
        id: this.selected_database,
      });
      this.all_tables = response.data.tables.map(table => ({ label: table.name, columns: table.columns.map(column => ({ ...column, label: column.name, alias: bi_query_builder.constructColumnAlias({ table_name: table.name, column_name: column.name }) })) }));
      await this.selectTable(this.all_tables[2]); // Remove this after integration the table selector popup
      return response.data;
    },
    async getTableColumns({ table_name }) {
      const response = await this.$services.bi_schema.columns({
        id: this.selected_database,
        table_name,
      });
      return response.data;
    },
    async selectTable(table) {
      const columns = table.columns.map(column => ({ ...column, label: column.name, alias: bi_query_builder.constructColumnAlias({ column_name: column.name }) }));

      const columns_with_hierarchy = [...columns, {
        label: 'General',
        is_hierarchy: true,
        children: [
          {
            label: 'Field 1',
            is_hierarchy: true,
            children: [
              columns[2],
              columns[3],
            ],
          },
          columns[0],
          columns[1],
        ],
      }];
      this.selected_table = {
        label: table.label,
        columns,
        columns_with_hierarchy,
      };
    },
    async getDataFromStages() {
      if (this.stages[0]?.value?.columns?.length === 0) {
        this.data = [];
        return;
      }
      const response = await this.$services.bi_query.execute({
        id: this.selected_database,
        body: this.stage_config,
      });
      this.data = response.data?.data;
      this.table_metadata = response.data?.metadata;
      this.sql_query = response.data?.sql;
      return response.data;
    },
    async getColumnValues({ table_name, column_name, query }) {
      const response = await this.$services.bi_schema.column_values({
        id: this.selected_database,
        table_name,
        column_name,
        query,
      });
      return response.data;
    },
  },
});

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useBiStore, import.meta.hot));
