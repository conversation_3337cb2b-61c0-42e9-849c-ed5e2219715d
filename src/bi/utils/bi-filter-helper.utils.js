export function getFilterText(column_config, filter_config) {
  console.log(':::getFilterText::', column_config, filter_config);
  const column_label = column_config.agg ? column_config.alias : column_config.label;
  if (!filter_config) {
    return column_label;
  }

  if (filter_config.operator.valueType === 'none') {
    return `${column_label} ${filter_config.operator.label}`;
  }
  else if (filter_config.operator.operator === 'between' && !Array.isArray(filter_config?.operator_value)) {
    return `${column_label} is ${filter_config.operator.label} ${filter_config.operator_value_min} and ${filter_config.operator_value_max}`;
  }
  else if (filter_config.operator.operator === 'between' && Array.isArray(filter_config.operator_value)) {
    return `${column_label} is ${filter_config.operator.label} ${filter_config.operator_value[0]} and ${filter_config.operator_value[1]}`;
  }
  else if (['equals', 'not_equals', 'greater_than', 'less_than'].includes(filter_config.operator.operator)) {
    return `${column_label} is ${filter_config.operator.label} ${Array.isArray(filter_config.operator_value) ? `${filter_config.operator_value.length} values` : filter_config.operator_value}`;
  }
  else if (filter_config.operator.operator === 'in') {
    return `${column_label} is ${filter_config.operator_value.length} selection/s`;
  }
  else if (filter_config.operator.operator === 'not_in') {
    return `${column_label} is not ${filter_config.operator_value.length} selection/s`;
  }
  else {
    return column_label;
  }
}
