import {
  COLOR_PALETTES,
  HEATMAP_PALETTES,
} from '@sensehawk/chart-generator/colors';

export const BI_DEFAULT_PALETTE_COLORS = COLOR_PALETTES.default;

export const BI_CHART_COLOR_PALETTES = Object.keys(COLOR_PALETTES).reduce((acc, key) => {
  acc[key] = {
    colors: COLOR_PALETTES[key],
  };
  return acc;
}, {});

export const BI_HEATMAP_PALETTES = Object.keys(HEATMAP_PALETTES).reduce((acc, key) => {
  acc[key] = {
    colors: HEATMAP_PALETTES[key],
  };
  return acc;
}, {});

export const BI_CHART_BUILDER_TABS = {
  pivot_table: ['layout', 'display', 'conditional_formatting'],
  table: ['layout', 'display', 'conditional_formatting'],
  column_chart: ['layout', 'display', 'axes', 'advanced'],
  horizontalBar_chart: ['layout', 'display', 'axes', 'advanced'],
  line_chart: ['layout', 'display', 'axes', 'advanced'],
  area_chart: ['layout', 'display', 'axes', 'advanced'],
  mixed_chart: ['layout', 'display', 'axes', 'advanced'],
  pie_chart: ['layout', 'display'],
  donut_chart: ['layout', 'display'],
  scatter_chart: ['layout', 'display', 'axes'],
  gauge_chart: ['layout', 'display', 'advanced'],
  progress_chart: ['layout', 'display', 'advanced'],
  heatmap_chart: ['layout', 'display'],
  pyramid_chart: ['layout', 'display'],
  funnel_chart: ['layout', 'display'],
  pareto_chart: ['layout', 'display'],
  waterfall_chart: ['layout', 'display', 'axes'],
};

export const CHART_TO_CATEGORY_TYPE_MAP = {
  column_chart: ['text', 'date', 'boolean'],
  horizontalBar_chart: ['text', 'date', 'boolean'],
  line_chart: ['text', 'date', 'boolean'],
  area_chart: ['text', 'date', 'boolean'],
  mixed_chart: ['text', 'date', 'boolean'],
  pie_chart: ['text'],
  donut_chart: ['text'],
  scatter_chart: ['numeric'],
  heatmap_chart: ['text'],
  pyramid_chart: ['text'],
  funnel_chart: ['text'],
  pareto_chart: ['text'],
  waterfall_chart: ['text'],
};

export const CHART_TO_VALUE_TYPE_MAP = {
  column_chart: ['numeric'],
  horizontalBar_chart: ['numeric'],
  line_chart: ['numeric'],
  area_chart: ['numeric'],
  mixed_chart: ['numeric'],
  pie_chart: ['numeric'],
  donut_chart: ['numeric'],
  scatter_chart: ['numeric'],
  heatmap_chart: ['numeric'],
  gauge_chart: ['numeric'],
  progress_chart: ['numeric'],
  pyramid_chart: ['numeric'],
  funnel_chart: ['numeric'],
  pareto_chart: ['numeric'],
  waterfall_chart: ['numeric'],
};

// These are also the only charts that support scale types, tick labels, custom ranges
export const SERIES_SUPPORTED_CHARTS = ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'];

// SERIES_SUPPORTED_CHARTS minus the horizontalBar_chart (because horizontal bar charts can't have dual y-axis)
export const DUAL_Y_AXIS_SUPPORTED_CHARTS = ['column_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'];

export const AXES_NAMES_SUPPORTED_CHARTS = ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'waterfall_chart', 'scatter_chart', 'pareto_chart', 'heatmap_chart'];
