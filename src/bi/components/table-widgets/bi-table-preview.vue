<script setup>
import { get, orderBy } from 'lodash-es';
import { computed, nextTick, onMounted, reactive } from 'vue';
import { useBiStore } from '~/bi/store/bi.store';
import BiHandsontable from './bi-handsontable.vue';

const props = defineProps({
  id: {
    type: String,
    default: () => crypto.randomUUID(),
  },
  chartType: {
    type: String,
    default: '',
  },
});

const bi_store = useBiStore();

const state = reactive({
  loading: false,
  error: null,
  data: [],
  columns: [],

  table_columns: [],
  table_data: [],
  nested_headers: [],
  nested_rows: false,
  column_config: {},

  table_instance: null,

  changes_detected: false,
  loading_changes: false,
});

const is_preview = computed(() => props.chartType === '');
const is_pivot_table = computed(() => props.chartType === 'pivot_table');
const table_settings = computed(() => {
  if (is_pivot_table.value) {
    return {
      ...(bi_store.pivot_table_preview_config.show_grand_totals ? { fixedRowsBottom: 1 } : {}),
      // ...(is_pivot_table.value ? ({ fixedColumnsStart: bi_store.pivot_table_preview_config.rows.length }) : {}),
    };
  }
  return {};
});
const enable_row_headers = computed(() => {
  if (is_pivot_table.value || is_preview.value)
    return false;
  return bi_store.table_preview_config.show_row_headers || false;
});
// -------------------------------- Methods --------------------------------- //
function getTableColumn(col) {
  let width = null;
  if (typeof col === 'string') {
    if (props.chartType === 'table') {
      width = bi_store.table_preview_config.columns_map[col]?.width || 150;
    }
    return {
      label: col,
      data: col,
      type: 'text',
      readOnly: true,
      ...(width && { width }),
    };
  }
  if (props.chartType === 'table') {
    width = bi_store.table_preview_config.columns_map[col.key || col.label]?.width || 150;
  }
  return {
    label: col.label,
    data: col.key || col.label,
    type: 'text',
    readOnly: true,
    ...(width && { width }),
  };
}

async function loadTableData(refreshTable = false) {
  state.data = bi_store.data;
  state.columns = (bi_store.table_metadata?.columns || [])?.map(col => col.name);

  if (!is_pivot_table.value) {
    if (!refreshTable) {
      bi_store.table_preview_config.columns_map = state.columns.reduce((acc, col_key, index) => {
        const col_data = bi_store.table_preview_config.columns_map[col_key] || {};
        acc[col_key] = {
          key: col_key,
          width: get(col_data, 'width', 150),
          visible: get(col_data, 'visible', true),
          order_index: get(col_data, 'order_index', index),
        };
        return acc;
      }, {});
    }
    if (is_preview.value) {
      state.table_columns = state.columns.map(col => getTableColumn(col));
    }
    else {
      state.table_columns = orderBy(
        state.columns.filter(col =>
          get(bi_store.table_preview_config.columns_map, `${col}.visible`, true),
        ),
        col => get(bi_store.table_preview_config.columns_map, `${col}.order_index`, 0),
      ).map(col => getTableColumn(col));
    }
    state.table_data = state.data;
    if (is_preview.value) {
      state.column_config = state.columns.reduce((acc, col) => {
        const column_details = bi_store.alias_to_column_mapping[col];
        if (column_details?.agg) {
          acc[col] = { backgroundColor: '#FFFAEB' };
        }
        return acc;
      }, {});
    }
    bi_store.is_table_dirty = false;
    return;
  }

  // Apply any data formatting and grouping
  if (!refreshTable) {
    const key_groups = state.columns.reduce((acc, col) => {
      const column_details = bi_store.alias_to_column_mapping[col];
      if (column_details?.agg) {
        acc.value_keys.push(col);
      }
      else {
        acc.non_value_keys.push(col);
      }
      return acc;
    }, { value_keys: [], non_value_keys: [] });
    const half = Math.ceil(key_groups.non_value_keys.length / 2);
    bi_store.pivot_table_preview_config.rows = key_groups.non_value_keys.slice(0, half);
    bi_store.pivot_table_preview_config.columns = key_groups.non_value_keys.slice(half);
    bi_store.pivot_table_preview_config.values = key_groups.value_keys;
  }

  const nested_headers = await generateNestedTableHeaders(
    state.data,
    bi_store.pivot_table_preview_config.columns,
    bi_store.pivot_table_preview_config.values,
    bi_store.pivot_table_preview_config.rows,
  );
  state.table_columns = nested_headers.slice(-1)[0].map(col => getTableColumn(col));
  state.nested_headers = nested_headers;

  const nested_data = await generateHandsontableData(
    state.data,
    bi_store.pivot_table_preview_config.columns,
    bi_store.pivot_table_preview_config.values,
    bi_store.pivot_table_preview_config.rows,
  );

  if (bi_store.pivot_table_preview_config.rows?.length)
    state.nested_rows = true;

  state.table_data = nested_data;
  bi_store.is_table_dirty = false;
}

async function generateNestedTableHeaders(data, columns, values, rowHeaders, delimiter = '|') {
  const nestedHeaders = [];

  // Handle case: no column pivoting
  if (columns.length === 0) {
    const flatHeaderRow = [];

    // Add row headers
    for (const rh of rowHeaders) {
      flatHeaderRow.push({ label: rh });
    }

    // Add values directly as columns
    for (const val of values) {
      flatHeaderRow.push({ label: val, key: val });
    }

    if (bi_store.pivot_table_preview_config.show_row_totals) {
      for (const val of values) {
        flatHeaderRow.push({ label: `Total ${val}`, key: `__row_total_${val}` });
      }
    }

    nestedHeaders.push(flatHeaderRow);
    return nestedHeaders;
  }

  // Build tree structure for column pivoting
  function buildTree(data, level = 0) {
    if (level >= columns.length)
      return [];

    const groups = {};
    for (const row of data) {
      const key = row[columns[level]];
      if (!groups[key])
        groups[key] = [];
      groups[key].push(row);
    }

    const nodes = [];
    for (const key in groups) {
      const children = buildTree(groups[key], level + 1);
      nodes.push({ label: key, children });
    }
    return nodes;
  }

  const tree = buildTree(data);

  function countLeaves(node) {
    if (!node.children || node.children.length === 0)
      return 1;
    return node.children.reduce((sum, child) => sum + countLeaves(child), 0);
  }

  // Generate nested headers
  function fillHeaders(nodes, level = 0) {
    if (!nestedHeaders[level]) {
      nestedHeaders[level] = [];
      for (let i = 0; i < rowHeaders.length; i++) {
        nestedHeaders[level].push({ label: '', colspan: 1 });
      }
    }

    for (const node of nodes) {
      const leafCount = countLeaves(node);
      nestedHeaders[level].push({
        label: node.label,
        colspan: leafCount * values.length,
      });

      if (node.children.length > 0) {
        fillHeaders(node.children, level + 1);
      }
    }
  }

  fillHeaders(tree);

  const finalRow = [];
  for (const rh of rowHeaders) {
    finalRow.push({ label: rh, key: rh });
  }

  function pushLeafLabels(nodes, path = []) {
    for (const node of nodes) {
      const currentPath = [...path, node.label];
      if (node.children.length > 0) {
        pushLeafLabels(node.children, currentPath);
      }
      else {
        for (const val of values) {
          finalRow.push({
            label: val,
            key: currentPath.concat(val).join(delimiter),
          });
        }
      }
    }
  }

  pushLeafLabels(tree);
  if (bi_store.pivot_table_preview_config.show_row_totals) {
    for (let i = 1; i <= nestedHeaders.length; i++) {
      nestedHeaders[i - 1].push({ label: i === nestedHeaders.length ? 'Row Total' : '', colspan: values.length });
    }
    for (const val of values) {
      finalRow.push({
        label: `Total ${val}`,
        key: `__row_total_${val}`,
      });
    }
  }
  nestedHeaders.push(finalRow);
  return nestedHeaders;
}

function generateHandsontableData(data, columns, values, rowHeaders, delimiter = '|') {
  // Helper: group rows recursively
  function groupByHeaders(rows, headers) {
    if (headers.length === 0)
      return rows.map(row => formatRow(row));

    const [current, ...rest] = headers;
    const groups = {};

    rows.forEach((row) => {
      const key = row[current];
      if (!groups[key])
        groups[key] = [];
      groups[key].push(row);
    });

    return Object.entries(groups).map(([key, rows]) => {
      const node = { [current]: key };
      if (rest.length > 0) {
        node.__is_group = true;
        node.__is_group_column = current;
        node.__children = groupByHeaders(rows, rest);

        if (bi_store.pivot_table_preview_config.show_column_totals || bi_store.pivot_table_preview_config.show_row_totals) {
          for (const row of rows) {
            if (bi_store.pivot_table_preview_config.show_column_totals) {
              // calculate column totals for each group
              const row_keys = getRowKeys(row);
              for (const key of row_keys) {
                const existing = Number(node[key.data_key] ?? 0);
                const current = Number(row[key.value_key] ?? 0);

                node[key.data_key] = existing + (Number.isNaN(current) ? 0 : current);
                node[key.data_key] = String(node[key.data_key]);
                node.__is_row_total = true;
              }
            }

            if (bi_store.pivot_table_preview_config.show_row_totals) {
              // calculate row totals for each group
              for (const val of values) {
                const existing = Number(node[`__row_total_${val}`] ?? 0);
                const current = Number(row[val] ?? 0);

                node[`__row_total_${val}`] = existing + (Number.isNaN(current) ? 0 : current);
                node[`__row_total_${val}`] = String(node[`__row_total_${val}`]);
                node.__is_row_total = true;
              }
            }
          }
        }
      }
      else {
        for (const row of rows) {
          formatRow(row, node);
        }
      }
      return node;
    });
  }

  function getRowKeys(row) {
    const column_key = columns.length
      ? columns.map(col => row[col]).join(delimiter)
      : '';
    const row_keys = [];
    for (const val of values) {
      if (column_key.length)
        row_keys.push({ data_key: `${column_key}${delimiter}${val}`, value_key: val });
      else
        row_keys.push({ data_key: val, value_key: val });
    }
    return row_keys;
  }

  // Assign values to a node
  function formatRow(row, node = {}) {
    const row_keys = getRowKeys(row);
    for (const key of row_keys) {
      node[key.data_key] = row[key.value_key] ?? null;
    }
    node.__actual_row = row;

    // compute row total at leaf level
    if (bi_store.pivot_table_preview_config.show_row_totals) {
      for (const val of values) {
        node[`__row_total_${val}`] = row[val] ?? null;
      }
    }
    return node;
  }

  const nested_data = groupByHeaders(data, rowHeaders);

  // --- Grand Total Column ---
  if (bi_store.pivot_table_preview_config.show_grand_totals) {
    const grand_total = { [rowHeaders[0] || 'Total']: 'Grand Totals', __is_grand_total: true };

    for (const row of data) {
    // aggregate per-column values
      const row_keys = getRowKeys(row);
      for (const key of row_keys) {
        const existing = Number(grand_total[key.data_key] ?? 0);
        const current = Number(row[key.value_key] ?? 0);
        grand_total[key.data_key] = existing + (Number.isNaN(current) ? 0 : current);
        grand_total[key.data_key] = String(grand_total[key.data_key]);
      }

      // aggregate per-row totals
      for (const val of values) {
        const existing = Number(grand_total[`__row_total_${val}`] ?? 0);
        const current = Number(row[val] ?? 0);
        grand_total[`__row_total_${val}`] = existing + (Number.isNaN(current) ? 0 : current);
        grand_total[`__row_total_${val}`] = String(grand_total[`__row_total_${val}`]);
      }
    }
    nested_data.push(grand_total);
  }

  return nested_data;
}

function getCellFormatting({ column, row_data }) {
  // For total rows and columns styles
  if (row_data.__is_grand_total) {
    return {
      'background-color': '#475467',
      'font-weight': 600,
      'color': '#FFFFFF',
    };
  }
  if (row_data.__is_row_total || column?.data?.includes('__row_total_')) {
    return {
      'background-color': '#EAECF0',
      'font-weight': 600,
      'color': '#101828 !important',
    };
  }
  return null;
}

function handleColumnResize(col_widths_map) {
  Object.entries(col_widths_map).forEach(([col_key, width]) => {
    if (bi_store.table_preview_config.columns_map[col_key]) {
      // Update width in the columns map
      bi_store.table_preview_config.columns_map[col_key] = {
        ...bi_store.table_preview_config.columns_map[col_key],
        width,
      };
    }
  });
}

async function fetchTableData(refreshTable = false) {
  try {
    state.loading_changes = true;
    if (!refreshTable)
      await bi_store.getDataFromStages();
    loadTableData(refreshTable);
  }
  catch (error) {
    logger.error(error);
  }
  finally {
    state.changes_detected = false;
    state.loading_changes = false;
  }
}

function autoFitColumns(hot) {
  if (props.chartType === 'table')
    return;
  nextTick(() => {
    const plugin = hot.getPlugin('autoColumnSize');
    plugin.recalculateAllColumnsWidth();
    const widths = [];
    for (let col = 0; col < hot.countCols(); col++) {
      widths.push(plugin.getColumnWidth(col));
    }

    hot.updateSettings({ colWidths: widths });
    hot.render();
  });
}

watch(() => bi_store.config_change_detection_counter, () => {
  state.changes_detected = true;
});
// -------------------------------- Lifecycle ------------------------------- //
onMounted(() => {
  loadTableData();
});
</script>

<template>
  <div class="h-[100%] w-full">
    <div
      v-if="state.changes_detected || bi_store.is_table_dirty"
      class="flex items-center justify-center w-full h-full"
    >
      <!-- Popup card -->
      <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6 flex flex-col items-center min-w-[600px]">
        <HawkFeaturedIcon theme="light-circle-outline" size="md" color="primary" class="mb-4">
          <IconHawkInfoCircle />
        </HawkFeaturedIcon>

        <div class="text-sm font-semibold mb-1">
          {{ $t('Changes detected') }}
        </div>

        <div class="text-sm text-gray-600 mb-6">
          {{ $t('New changes is been detected. You can reload to view new changes.') }}
        </div>

        <HawkButton :loading="state.loading_changes" @click="fetchTableData(bi_store.is_table_dirty)">
          <IconHawkRefreshCwTwo />
          <span class="text-sm font-medium">
            {{ $t('Load Preview') }}
          </span>
        </HawkButton>
      </div>
    </div>
    <div v-else-if="!state.table_data?.length">
      <div class="flex items-center justify-center w-full h-full">
        <HawkIllustrations type="no-data" for="bi-table" />
      </div>
    </div>
    <BiHandsontable
      v-else
      :bi-table-id="`preview-table-${props.id}`"
      :data="state.table_data"
      :columns="state.table_columns"
      :column-config="state.column_config"
      :nested-headers="state.nested_headers"
      :nested-rows="state.nested_rows"
      :show-skeleton-loader="state.loading"
      :get-cell-formatting="getCellFormatting"
      :row-headers="enable_row_headers"
      :additional-table-settings="table_settings"
      class="h-full"
      :enable-column-sorting="!is_pivot_table"
      @table-instance="state.table_instance = $event"
      @after-load-data="autoFitColumns"
      @column-resize="handleColumnResize"
    />
  </div>
</template>

<style scoped lang="scss">
// Add any custom styles here
</style>
