<script setup>
import { watchDebounced } from '@vueuse/core';
import { storeToRefs } from 'pinia';
import { nextTick, onBeforeUnmount } from 'vue';
import { useBiChartBuilderHelpers } from '~/bi/composables/bi-chart-builder-helpers.composable';
import { AXES_NAMES_SUPPORTED_CHARTS, BI_HEATMAP_PALETTES, DUAL_Y_AXIS_SUPPORTED_CHARTS, SERIES_SUPPORTED_CHARTS } from '~/bi/constants/bi-constants';
import { useBiStore } from '~/bi/store/bi.store';
import { generateChart } from '~/bi/utils/bi-helper.utils.js';
import BiBottomDrawer from './bi-bottom-drawer.vue';
import BiTablePreview from './table-widgets/bi-table-preview.vue';

const emit = defineEmits(['continue']);

const { markers_value_options } = useBiChartBuilderHelpers();

const bi_store = useBiStore();
const { chart_builder_config, data, are_chart_builder_fields_filled, chart_builder_data_types } = storeToRefs(bi_store);

let chart_instance = null;

async function renderWidget() {
  if (chart_builder_config.value.chart_type === 'table') {
    // Probably some data processing
  }
  else {
    const config = {
      type: chart_builder_config.value.chart_type.replace('_chart', ''),
    };

    // Layout tab
    const values = Array.isArray(chart_builder_config.value.layout_values)
      ? chart_builder_config.value.layout_values
      : [{ value: chart_builder_config.value.layout_values }];
    if (!are_chart_builder_fields_filled.value) {
      chart_instance?.dispose?.();
      return;
    }
    await nextTick();
    config.data = {
      category: chart_builder_config.value.layout_category,
      values: values.map(item => item.value),
      ...(
        (['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'heatmap_chart'].includes(chart_builder_config.value.chart_type))
          ? {
              stackBy: chart_builder_config.value.stack_by === 'none' ? null : chart_builder_config.value.stack_by,
            }
          : {}),
    };

    // Series config
    if (chart_builder_config.value.layout_values && ['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'].includes(chart_builder_config.value.chart_type)) {
      config.series = {
        ...Object.values(chart_builder_config.value.layout_values).reduce((acc, item) => {
          acc[item.value] = {
            name: item.legend || item.value,
            type: item.chart_type,
            ...(chart_builder_config.value.chart_type !== 'horizontalBar_chart' ? { yAxisIndex: item.y_axis === 'primary' ? 0 : 1 } : {}),
            color: item.color,
            ...(!item.stack ? { stack: false } : {}),
            lineColor: item.color,
            lineWidth: Number.parseInt(item.line_width),
            lineStyle: item.line_style,
            smooth: item.line_shape === 'curved',
            prefix: item.prefix,
            suffix: item.suffix,
          };
          return acc;
        }, {}),
      };
    }

    //  Display tab
    config.layout = {
      title: chart_builder_config.value.title,
      subtitle: chart_builder_config.value.subtitle,
    };
    config.legend = {
      show: chart_builder_config.value.legend !== 'hide',
      position: chart_builder_config.value.legend,
    };
    config.dataValues = {
      show: chart_builder_config.value.values === 'show',
      compact: chart_builder_config.value.compact,
      precision: chart_builder_config.value.precision,
    };

    // Axes tab
    config.axes = {
      ...AXES_NAMES_SUPPORTED_CHARTS.includes(chart_builder_config.value.chart_type)
        ? {
            categoryName: chart_builder_config.value.category_axis_name,
            valueName: chart_builder_config.value.value_axis_name,
            ...(chart_builder_config.value.secondary_y_axis ? { secondaryValueName: chart_builder_config.value.secondary_y_axis } : {}),
          }
        : {},
      ...(SERIES_SUPPORTED_CHARTS.includes(chart_builder_config.value.chart_type)
        ? {
            // Custom ranges
            ...(!Number.isNaN(Number.parseInt(chart_builder_config.value.custom_range_min)) ? { valueMin: Number.parseInt(chart_builder_config.value.custom_range_min) } : {}),
            ...(!Number.isNaN(Number.parseInt(chart_builder_config.value.custom_range_max)) ? { valueMax: Number.parseInt(chart_builder_config.value.custom_range_max) } : {}),
            ...(DUAL_Y_AXIS_SUPPORTED_CHARTS.includes(chart_builder_config.value.chart_type)
              ? {
                  ...(!Number.isNaN(Number.parseInt(chart_builder_config.value.secondary_value_min)) ? { secondaryValueMin: Number.parseInt(chart_builder_config.value.secondary_value_min) } : {}),
                  ...(!Number.isNaN(Number.parseInt(chart_builder_config.value.secondary_value_max)) ? { secondaryValueMax: Number.parseInt(chart_builder_config.value.secondary_value_max) } : {}),
                }
              : {}),
            // Tick labels (Orientation)
            categoryLabels: chart_builder_config.value.category_tick_label,
            valueLabels: chart_builder_config.value.value_tick_label,
            secondaryValueLabels: chart_builder_config.value.secondary_value_tick_label,
            // Scales (Linear, Log)
            valueScale: chart_builder_config.value.primary_scale,
            secondaryValueScale: chart_builder_config.value.secondary_scale,
          }
        : {}),
    };

    // Advanced tab
    if (
      ['column_chart', 'line_chart', 'area_chart', 'mixed_chart'].includes(chart_builder_config.value.chart_type)
      && chart_builder_config.value.layout_category
      && chart_builder_data_types.value[chart_builder_config.value.layout_category] === 'date'
    ) {
      config.interactions = {
        dataZoom: {
          enabled: chart_builder_config.value.timeseries,
          type: 'both',
        },
      };
    }

    if (chart_builder_config.value.reference_lines) {
      config.reference_lines = chart_builder_config.value.reference_lines.map(line => ({
        value: ['min', 'max', 'average', 'median', 'p25', 'p75', 'p90', 'p95'].includes(line.value) ? line.value : Number.parseInt(line.value),
        label: line.label,
        color: line.color,
        series: line.series,
        lineStyle: line.line_style,
      })).filter(line => line.value && line.series);
    }

    // Chart specific config
    if (chart_builder_config.value.chart_type === 'pareto_chart') {
      config.chartSpecific = {
        pareto: {
          show80PercentLine: chart_builder_config.value.show_eighty_percent_line,
          eightyPercentLineColor: chart_builder_config.value.eighty_percent_line_color,
          eightyPercentLineStyle: chart_builder_config.value.eighty_percent_line_style,
          barColor: chart_builder_config.value.bar_color,
          lineColor: chart_builder_config.value.cumulative_line_color,
        },
      };
    }
    else if (chart_builder_config.value.chart_type === 'heatmap_chart') {
      config.chartSpecific = {
        heatmap: {
          colorScheme: BI_HEATMAP_PALETTES[chart_builder_config.value.color_scheme]?.colors,
          colorType: chart_builder_config.value.color_type,
          ...(chart_builder_config.value.color_ranges?.length
            ? {
                colorPieces: chart_builder_config.value.color_ranges.map(range => ({
                  label: range.label,
                  min: markers_value_options.value.includes(range.min) ? range.min : Number.parseInt(range.min),
                  max: markers_value_options.value.includes(range.max) ? range.max : Number.parseInt(range.max),
                  color: range.color,
                })),
              }
            : {}),
        },
      };
    }
    else if (chart_builder_config.value.chart_type === 'waterfall_chart') {
      config.chartSpecific = {
        waterfall: {
          showSum: chart_builder_config.value.show_sum,
          positiveColor: chart_builder_config.value.positive_color,
          negativeColor: chart_builder_config.value.negative_color,
        },
      };
    }
    else if (chart_builder_config.value.chart_type === 'pyramid_chart') {
      config.chartSpecific = {
        pyramid: {
          labelsAtCenter: chart_builder_config.value.show_labels_at_center,
          showPercentages: chart_builder_config.value.show_percentages,
          is3D: chart_builder_config.value.render_in_3d,
        },
      };
    }
    else if (chart_builder_config.value.chart_type === 'funnel_chart') {
      config.chartSpecific = {
        funnel: {
          labelsAtCenter: chart_builder_config.value.show_labels_at_center,
          showPercentages: chart_builder_config.value.show_percentages,
          is3D: chart_builder_config.value.render_in_3d,
          percentOfPrevious: chart_builder_config.value.compare_with_previous,
          maintainSlope: chart_builder_config.value.maintain_slope,
        },
      };
    }
    else if (['gauge_chart', 'progress_chart'].includes(chart_builder_config.value.chart_type)) {
      config.chartSpecific = {
        [chart_builder_config.value.chart_type.replace('_chart', '')]: {
          ...(chart_builder_config.value.markers
            ? {
                markers: chart_builder_config.value.markers.map(marker => ({
                  label: marker.label,
                  value: markers_value_options.value.includes(marker.value) ? marker.value : Number.parseInt(marker.value),
                  color: marker.color,
                })),
              }
            : {}),
          ...(chart_builder_config.value?.color_ranges?.length
            ? {
                colorRange: chart_builder_config.value.color_ranges.map(range => ({
                  min: markers_value_options.value.includes(range.min) ? range.min : Number.parseInt(range.min),
                  max: markers_value_options.value.includes(range.max) ? range.max : Number.parseInt(range.max),
                  color: range.color,
                })),
              }
            : {}),
        },
      };
    }

    chart_instance = await generateChart('chart-container', data.value, config, chart_instance);
  }
}

function downloadWidget() {
  const imgData = chart_instance.getDataURL({
    type: 'png',
    pixelRatio: 2,
    backgroundColor: '#fff',
  });
  const a = document.createElement('a');
  a.href = imgData;
  a.download = 'chart.png';
  a.click();
}

watchDebounced(
  () => chart_builder_config.value,
  async () => {
    await nextTick();
    renderWidget();
  },
  { deep: true, immediate: true, debounce: 300 },
);

onBeforeUnmount(() => {
  if (chart_instance) {
    chart_instance.dispose();
    chart_instance = null;
  }
});
</script>

<template>
  <div class="h-full w-full flex flex-col relative">
    <div class="p-6 flex-1">
      <div v-if="!are_chart_builder_fields_filled" class="h-full w-full flex flex-col justify-center items-center text-center">
        <IconIllustrationBiEmptyChartBuilder />
        <div class="text-sm font-semibold text-gray-900 mt-4 mb-1">
          No chart to show
        </div>
        <div class="text-sm font-normal text-gray-600">
          Configure the chart from left panel to start generating the chart
        </div>
      </div>
      <div v-else-if="['table', 'pivot_table'].includes(chart_builder_config.chart_type)" class="w-full h-full">
        <BiTablePreview
          :key="chart_builder_config.chart_type"
          is-preview
          :chart-type="chart_builder_config.chart_type"
        />
      </div>
      <div v-else id="chart-container" class="h-full w-full" />
    </div>

    <hr>
    <div class="p-6 w-full flex justify-end items-center">
      <HawkButton type="outlined" class="mr-3" @click="downloadWidget">
        <IconHawkDownloadOne />
        Download (temporary)
      </HawkButton>
      <HawkButton @click="emit('continue')">
        <IconHawkRocketTwo />
        Publish widget
      </HawkButton>
    </div>

    <BiBottomDrawer
      show-text="Show results"
      hide-text="Hide results"
    >
      <template #default>
        TABLE
      </template>
    </BiBottomDrawer>
  </div>
</template>
