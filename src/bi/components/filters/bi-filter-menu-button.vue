<script setup>
import { storeToRefs } from 'pinia';
import BIFilter from '~/bi/components/filters/bi-filter.vue';
import BiFilterList from '~/bi/components/filters/bi-filter-list.vue';

defineProps({
  columns: {
    type: Array,
    required: true,
  },
});
const emit = defineEmits(['apply']);

const selected_column = ref(null);

function onFilterSelected(column) {
  selected_column.value = column;
}
function onClose() {
  selected_column.value = null;
}
function onApply($event, close = () => {}) {
  emit('apply', $event);
  selected_column.value = null;
  close?.();
}

onMounted(() => {
});
</script>

<template>
  <div>
    <HawkMenu
      additional_trigger_classes="!ring-0 !focus:ring-0"
      :items="[]"
      position="fixed"
      @close="onClose"
    >
      <!-- @open="onOpen"
      @close="onClose" -->
      <template #trigger="{ open, close }">
        <slot name="trigger_label" />
      </template>
      <template #content="{ close: closeOperatorMenu }">
        <div class="bg-white p-1 rounded-lg w-[356px]">
          <!-- <BiFilterList v-if="!selected_column" :columns="DUMMY_COLUMNS" @filter-selected="onFilterSelected" /> -->
          <BiFilterList v-if="!selected_column" :columns="columns" @filter-selected="onFilterSelected" />
          <BIFilter v-else :selected-column-config="selected_column" @cancel="closeOperatorMenu" @back="selected_column = null" @apply="($event) => onApply($event, closeOperatorMenu)" />
        </div>
      </template>
    </HawkMenu>
  </div>
</template>
