<script setup>
import BIFilter from '~/bi/components/filters/bi-filter.vue';
import { getFilterText } from '~/bi/utils/bi-filter-helper.utils.js';

// --------------------------------- Imports -------------------------------- //

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  columns: {
    type: Array,
    default: () => ([]),
  },
  existingColumnFilters: {
    type: Array,
    default: () => ([]),
  },
  // canRemoveFilter: {
  //   type: Boolean,
  //   default: false,
  // },
});

// ---------------------------------- Emits --------------------------------- //
const emit = defineEmits(['filterSelected', 'removeFilterField', 'editAppliedFilter']);

// --------------------- Variables - Pinia - storeToRefs -------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //

// ------------------------ Variables - Local - refs ------------------------ //

// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //
// const columns =

const columns = computed(() => {
  // If columns is empty and has existing filters, we need to show only those columns with filters in list
  if (props.columns.length === 0 && props.existingColumnFilters.length > 0) {
    return props.existingColumnFilters;
  }
  else if (props.columns.length && props.existingColumnFilters.length === 0) {
    return props.columns.map(column => ({ column_config: column, filter_config: null }));
  }

  // If we have both show both after updating the data with the existing filters
  return props.columns.map((column) => {
    const existing_filter = props.existingColumnFilters.find(filter => filter.column_config.uid === column.uid);
    if (existing_filter) {
      column.filter = existing_filter;
    }
    return column;
  });
});
// -------------------------------- Functions ------------------------------- //

// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
// if (props.columns.length === 0)
// console.log('Filter List Component Loaded::', props, columns.value);
</script>

<template>
  <div>
    <!-- HEllo -->
    <!-- {{ columns }} -->
    <template v-if="props.columns.length && props.existingColumnFilters.length === 0">
      <div v-for="column in columns" :key="column.column_config.uid" class="group hover:bg-gray-50 cursor-pointer flex justify-between p-2 rounded-lg" @click="$emit('filterSelected', column.column_config)">
        <div class="text-gray-600 text-sm">
          <!-- {{ column.column_config.label }} -->
          {{ getFilterText(column.column_config, null) }}
        </div>
        <div class="group-hover:block hidden">
          <Icon-hawk-x-close class="cursor-pointer text-gray-600 text-sm" />
        </div>
      </div>
    </template>
    <template v-else-if="props.columns.length === 0 && props.existingColumnFilters.length > 0">
      <HawkMenu
        v-for="(field, index) in columns"
        :key="field.column_config.label"
        class="flex items-center justify-between gap-2 p-2 ml-6 cursor-pointer group hover:bg-gray-50 rounded-lg"
        additional_trigger_classes="!ring-0 !focus:ring-0 !w-full"
        :items="[]"
        position="fixed"
      >
        <template #trigger="{ open, close }">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <span class="text-sm text-gray-600">{{ getFilterText(field.column_config, field.filter_config) }}</span>
            </div>
            <div class="text-gray-500 hidden group-hover:block mr-2" @click.stop="emit('removeFilterField', index)">
              <IconHawkXClose class="size-4" />
            </div>
          </div>
        </template>
        <template #content="{ close: closeOperatorMenu }">
          <div class="bg-white p-1 rounded-lg w-[356px]">
            <BIFilter :selected-column-config="field.column_config" :filter-config="field.filter_config" :has-back="false" @cancel="closeOperatorMenu" @apply="(event$) => { emit('editAppliedFilter', event$, index, closeOperatorMenu) }" />
          </div>
        </template>
      </HawkMenu>
    </template>
  </div>
</template>

<style scoped lang="scss">

</style>
