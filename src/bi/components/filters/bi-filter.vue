<script setup>
import { useBIFilterMaps } from '~/bi/composables/useBIFilterMaps';
import { useBiStore } from '~/bi/store/bi.store';

// --------------------------------- Imports -------------------------------- //

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  selectedColumnConfig: {
    type: Object,
    required: true,
  },
  filterConfig: {
    type: Object,
    default: () => ({}),
  },
  hasBack: {
    type: Boolean,
    default: true,
  },
});

// ---------------------------------- Emits --------------------------------- //
const emit = defineEmits(['apply', 'back', 'cancel', 'search']);

// ---------------------------- Injects/Provides ---------------------------- //

// ----------------------- Variables - Pinia - consts ----------------------- //
const { getTypeOperatorMap, operators_config_map, getOperatorComponent } = useBIFilterMaps();
const bi_store = useBiStore();

// --------------------- Variables - Pinia - storeToRefs -------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //
const type_operator_map = getTypeOperatorMap();
const available_operators = type_operator_map[props.selectedColumnConfig.type].map(operator_name => operators_config_map[operator_name]);

// ------------------------ Variables - Local - refs ------------------------ //
const form$ = ref(null);
const search = ref('');
const is_column_options_loading = ref(true);
const selected_operator = ref(props.filterConfig?.operator || available_operators[0] || null);
const column_options = ref([]);

// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //
const current_component = computed(() => {
  return getOperatorComponent({
    selected_operator: selected_operator.value,
    selected_column_config: props.selectedColumnConfig,
    column_options: column_options.value,
  });
});
// -------------------------------- Functions ------------------------------- //
async function onApply() {
  await form$.value.validate();
  console.log('Form data:', form$.value.invalid);
  if (form$.value.invalid)
    return;
  console.log('Apply clicked', form$.value.data, 'operator_type::', selected_operator.value);
  emit('apply', {
    column_config: props.selectedColumnConfig,
    filter_config: {
      operator: selected_operator.value,
      ...form$.value.data,
    },
  });
};
function onBack() {
  // console.log('Back clicked');
  emit('back');
};
function onCancel() {
  // console.log('Cancel clicked');
  emit('cancel');
};
async function fetchColumnValues(query = {}) {
  is_column_options_loading.value = true;
  const { values } = await bi_store.getColumnValues({
    table_name: props.selectedColumnConfig.table_name,
    column_name: props.selectedColumnConfig.name,
    query,
    // query: { limit: 100 },
  });
  is_column_options_loading.value = false;
  return values;
}
function onSelectOperator(operator, close = () => {}) {
  // console.log('Select operator clicked', operator);
  selected_operator.value = operator;
  close?.();
};
async function onSearch(val) {
  emit('search', val);

  const column_values = await fetchColumnValues({ search: val });
  column_options.value = column_values;
}
// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
onMounted(async () => {
  // console.log('FETCH');
  const column_values = await fetchColumnValues();
  column_options.value = column_values;

  // console.log('Column values:', column_values);
});
console.log('Selected Column Config:', props.selectedColumnConfig, props.filterConfig, type_operator_map, available_operators);
</script>

<template>
  <div>
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <HawkButton v-if="hasBack" type="plain" size="xs" class="!px-2 !py-0" @click="onBack">
          <icon-hawk-arrow-left class="text-gray-600  " />
        </HawkButton>
        <div class="text-sm font-semibold text-gray-700 ml-2">
          {{ selectedColumnConfig.label }}
        </div>
      </div>
      <HawkMenu
        additional_trigger_classes="!ring-0 !focus:ring-0 !px-2 !py-0"
        :items="available_operators"
        position="fixed"
      >
        <template #trigger="{ open }">
          <div class="p-2 w-full flex items-center flex-1 gap-2 text-xs">
            {{ selected_operator.label }}
            <div>
              <IconHawkChevronUp v-if="open" />
              <IconHawkChevronDown v-else />
            </div>
          </div>
        </template>
        <template #content="{ close: closeOperatorMenu }">
          <div class="bg-white p-1 rounded-lg max-h-60 scrollbar">
            <div
              v-for="operator in available_operators" :key="operator.operator"
              class="px-3 h py-3 text-xs font-medium min-w-[10rem] rounded-lg cursor-pointer text-gray-700 hover:bg-gray-50 flex items-center"
              @click.stop="onSelectOperator(operator, closeOperatorMenu)"
            >
              {{ operator.label }}
              <IconHawkCheck v-if="selected_operator.operator === operator.operator" class="ml-auto text-primary-500 w-5 h-5 min-w-5" />
            </div>
          </div>
        </template>
      </HawkMenu>
    </div>
    <!-- Operator config -->
    <div class="mt-4 mb-2 max-h-96">
      <Vueform ref="form$" size="sm" class="px-2" :display-errors="false">
        <HawkSearchInput
          v-if="selected_operator?.valueType === 'array'"
          v-model="search" :placeholder="$t('Search')" class="mb-4 px-2"
          :debounce_time="700"
          full_width
          @update:model-value="onSearch"
          @keydown.stop
        />
        <span v-if="column_options?.length === 0 && !is_column_options_loading" class="text-xs text-gray-400 col-span-12 px-2">{{ $t('No results found') }}</span>
        <HawkLoader v-if="is_column_options_loading" class="col-span-12 !m-0" />
        <template v-else>
          <component
            :is="current_component.component"
            :key="column_options.length"
            class="col-span-12"
            v-bind="{
              componentConfig: current_component.componentConfig,
              columnConfig: current_component.columnConfig,
              filterConfig: props.filterConfig,
            }"
          >
          <!--
          @change="handleChange"
          @on-enter="handleEnter"
          @on-click="handleClick" -->
          <!-- <template v-if="$slots.item" #item="slotProps">
              <slot name="item" v-bind="slotProps" />
            </template> -->
          </component>
        </template>
        <!-- Footer -->
        <div class="col-span-12 mt-2">
          <hr>
          <div class="mt-2 flex items-center justify-end text-sm font-semibold">
            <div class="flex items-center">
              <div class="text-gray-600 cursor-pointer p-2" @click="onCancel">
                {{ $t('Cancel') }}
              </div>
            </div>
            <div class="text-primary-700 cursor-pointer p-2" @click="onApply">
              {{ $t('Apply') }}
            </div>
          </div>
        </div>
      </Vueform>
    </div>
  </div>
</template>
