<script setup>
import { nextTick, onUnmounted, reactive } from 'vue';
import { generateChart } from '~/bi/utils/bi-helper.utils.js';

const props = defineProps({
  widgetId: {
    type: String,
    required: true,
  },
  data: {
    type: Array,
    required: true,
  },
  config: {
    type: Object,
    required: true,
  },
  isEditing: {
    type: Boolean,
    default: false,
  },
});

const state = reactive({
  resize_observer: null,
});

let chart_instance = null;

watch(() => [props.data, props.config], async () => {
  if (!props.widgetId || !props.data.length || !props.config)
    return;
  await nextTick();
  chart_instance = await generateChart(props.widgetId, props.data, props.config, chart_instance);

  const chartElement = document.getElementById(props.widgetId);
  state.resize_observer = new ResizeObserver(() => {
    // For echarts
    chart_instance?.resize?.();
    // For fusion charts
    const { width, height } = chartElement.getBoundingClientRect();
    chart_instance?.resizeTo?.(width, height);
  });
  state.resize_observer.observe(chartElement);
}, { deep: true, immediate: true });

onUnmounted(() => {
  const chartElement = document.getElementById(props.widgetId);
  if (chartElement) {
    state.resize_observer?.unobserve(chartElement);
  }
  chart_instance?.dispose();
});
</script>

<template>
  <div class="p-3 h-full w-full group">
    <div class="h-6 flex items-center justify-between gap-3">
      <div class="w-full text-sm font-medium text-gray-900 text-ellipsis overflow-hidden whitespace-nowrap">
        Solar Irradiance Tracker
      </div>
      <div v-if="props.isEditing" class="items-center gap-3 group-hover:flex hidden">
        <IconHawkEditFive
          v-tippy="{
            content: 'Edit data',
            placement: 'top',
          }"
          class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
        />
        <IconHawkBarChartTen
          v-tippy="{
            content: 'Edit chart',
            placement: 'top',
          }"
          class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
        />
        <IconHawkCopyFour
          v-tippy="{
            content: 'Duplicate',
            placement: 'top',
          }"
          class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
        />
        <IconHawkLogOutTwo
          v-tippy="{
            content: 'Move',
            placement: 'top',
          }"
          class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
        />
        <IconHawkTrashThree
          v-tippy="{
            content: 'Delete',
            placement: 'top',
          }"
          class="w-4 h-4 text-gray-600 outline-0 cursor-pointer"
        />
      </div>
    </div>
    <div :id="props.widgetId" class="h-[calc(100%-24px)] w-full" />
  </div>
</template>
