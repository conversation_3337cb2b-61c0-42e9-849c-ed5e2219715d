<script setup>
import chroma from 'chroma-js';
import { onMounted } from 'vue';
import { BI_DEFAULT_PALETTE_COLORS } from '~/bi/constants/bi-constants';

const props = defineProps({
  fields: {
    type: Array,
    required: true,
  },
  fieldsMap: {
    type: Object,
    default: () => ({}),
  },
  ruleDetails: {
    type: Object,
    default: () => ({}),
  },
});

const emits = defineEmits(['ruleChange']);

const form = ref(null);

const default_form_data = {
  field: props.fields[0],
  formatting_style: 'single_color',
  operator: 'equal',
  value: '',
  color: BI_DEFAULT_PALETTE_COLORS[0],
  apply_to: 'column',
  color_range: 5,
};

const field_details = computed(() => props.fieldsMap[form.value.field] || {});
const is_numeric = computed(() => ['integer'].includes(field_details.value.type));

function getOperators() {
  if (is_numeric.value) {
    return [
      { value: 'empty', label: 'Empty' },
      { value: 'not_empty', label: 'Not empty' },
      { value: 'equal', label: 'Equal to' },
      { value: 'not_equal', label: 'Not equal to' },
      { value: 'less_than', label: 'Less than' },
      { value: 'greater_than', label: 'Greater than' },
      { value: 'less_than_or_equal', label: 'Less than or equal to' },
      { value: 'greater_than_or_equal', label: 'Greater than or equal to' },
    ];
  }
  return [
    { value: 'empty', label: 'Empty' },
    { value: 'not_empty', label: 'Not empty' },
    { value: 'equal', label: 'Equal to' },
    { value: 'not_equal', label: 'Not equal to' },
    { value: 'contains', label: 'Contains' },
    { value: 'not_contains', label: 'Does not contain' },
    { value: 'starts_with', label: 'Starts with' },
    { value: 'ends_with', label: 'Ends with' },
  ];
}

function getFormColors() {
  if (form.value.formatting_style !== 'color_range')
    return [];
  const data = chroma.scale(['#ffffff', form.value.color]).mode('lch').colors(form.value.color_range);
  return data;
}

function updateColorRange(type) {
  if (type === 'increment')
    form.value.color_range = Number(form.value.color_range || 5) + 1;
  else
    form.value.color_range = Number(form.value.color_range || 5) - 1;
}

function updateOperator() {
  form.value.operator = 'equal';
}

function onSave() {
  emits('ruleChange', {
    uid: props.ruleDetails?.uid || crypto.randomUUID(),
    ...form.value,
  });
}

function init() {
  form.value = {
    ...default_form_data,
    ...(props.ruleDetails || {}),
  };
}
init();
</script>

<template>
  <div>
    <Vueform
      v-model="form"
      size="sm"
      sync
      :messages="{
        required: $t('This field is required'),
        numeric: $t('This field must be numeric'),
      }"
      :display-errors="false"
      :endpoint="onSave"
    >
      <div class="col-span-12">
        <div class="mb-4">
          <div class="text-sm font-semibold text-gray-900 mb-2">
            {{ $t('Column') }}
          </div>
          <SelectElement
            name="field"
            :items="props.fields"
            :default="props.fields[0]"
            :native="false"
            :can-clear="false"
            :can-deselect="false"
            placeholder="Select"
            @select="updateOperator()"
          />
        </div>

        <div class="mb-4">
          <div class="text-sm font-semibold text-gray-900 mb-2">
            {{ $t('Formatting style') }}
          </div>
          <RadiogroupElement
            name="formatting_style"
            :items="[
              { value: 'single_color', label: $t('Single color') },
              { value: 'color_range', label: $t('Color range') },
            ]"
            default="single_color"
          />
        </div>
        <div v-show="form?.formatting_style === 'single_color'" class="mb-6">
          <div class="text-sm font-semibold text-gray-900 mb-2">
            {{ $t('When a cell in this column') }}
          </div>
          <SelectElement
            name="operator"
            :items="getOperators()"
            class="mb-2"
            default="equal"
            :native="false"
            :can-clear="false"
            :can-deselect="false"
            placeholder="Select"
          />
          <TextElement
            v-if="is_numeric"
            name="value"
            :conditions="[
              ['operator', '!=', ['empty', 'not_empty']],
            ]"
            placeholder="0"
            :rules="['required', 'numeric']"
          />
          <TextElement
            v-else
            name="value"
            :conditions="[
              ['operator', '!=', ['empty', 'not_empty']],
            ]"
            :rules="['required']"
            placeholder="Enter value"
          />
          <div class="flex items-center justify-between py-2 mb-2">
            <div class="text-sm font-semibold text-gray-900">
              {{ $t('turn its background color to') }}
            </div>
            <BiColorPicker
              type="outlined"
              :active-color="form.color"
              @color-selected="form.color = $event"
            />
          </div>

          <div class="text-sm font-semibold mb-2">
            {{ $t('Apply color to') }}
          </div>
          <SelectElement
            name="apply_to"
            :items="[
              { value: 'column', label: $t('This column') },
              { value: 'entire_row', label: $t('Entire row') },
            ]"
            default="column"
            :native="false"
            :can-clear="false"
            :can-deselect="false"
          />
        </div>

        <div v-show="form?.formatting_style === 'color_range'" class="mb-6">
          <div class="text-sm font-semibold text-gray-900 mb-2">
            {{ $t('Colors') }}
          </div>

          <div class="flex items-center gap-2">
            <HawkButton icon type="plain" :disabled="form.value.color_range <= 2" @click="updateColorRange('decrement')">
              <IconHawkMinusCircle />
            </HawkButton>
            <BiColorPicker
              :active-color="form.color"
              class="pt-1"
              @color-selected="form.color = $event"
            >
              <template #trigger>
                <div class="flex rounded-md overflow-hidden border border-gray-200 w-[320px]">
                  <div
                    v-for="color in getFormColors()"
                    :key="color"
                    class="h-8"
                    :style="{
                      backgroundColor: color,
                      width: `${Math.floor(340 / getFormColors().length)}px`,
                    }"
                  />
                </div>
              </template>
            </BiColorPicker>
            <HawkButton icon type="plain" @click="updateColorRange('increment')">
              <IconHawkPlusCircle />
            </HawkButton>
          </div>
          <div class="text-sm font-semibold text-gray-900 mb-2 mt-4">
            {{ $t('Start the range at') }}
          </div>
          <RadiogroupElement
            name="start_range_at"
            class="mb-1"
            :items="[
              { value: 'min', label: $t('Smallest value in this column') },
              { value: 'custom', label: $t('Custom value') },
            ]"
            default="min"
          />
          <TextElement
            name="start_range_value"
            :conditions="[
              ['start_range_at', 'custom'],
            ]"
            :rules="['required', 'numeric']"
          />
          <div class="text-sm font-semibold text-gray-900 mb-2 mt-4">
            {{ $t('End the range at') }}
          </div>
          <RadiogroupElement
            name="end_range_at"
            class="mb-1"
            :items="[
              { value: 'max', label: $t('Largest value in this column') },
              { value: 'custom', label: $t('Custom value') },
            ]"
            default="max"
          />
          <TextElement
            name="end_range_value"
            :conditions="[
              ['end_range_at', 'custom'],
            ]"
            :rules="['required', 'numeric']"
          />
        </div>

        <!-- Custom fields -->
        <TextElement
          v-show="!form.formatting_style"
          name="color"
        />
        <TextElement
          v-show="!form.formatting_style"
          name="color_range"
          type="number"
        />

        <div class="border-b border-gray-200 mb-6" />

        <div class="flex justify-end mt-4">
          <ButtonElement
            name="save"
            secondary
            :button-label="$t('Save rule')"
            :submits="true"
          />
        </div>
      </div>
    </Vueform>
  </div>
</template>

<style scoped lang="scss">

</style>
