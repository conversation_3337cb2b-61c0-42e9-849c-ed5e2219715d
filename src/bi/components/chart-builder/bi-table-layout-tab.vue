<script setup>
import { orderBy } from 'lodash-es';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';
import { useBiStore } from '~/bi/store/bi.store';

const draggable = defineAsyncComponent(() => import('vuedraggable'));

const bi_store = useBiStore();
const { getIconsForType } = useBIQueryBuilder();

const columns = computed({
  get() {
    return orderBy(Object.values(bi_store.table_preview_config.columns_map), 'order_index');
  },
  set(value) {
    bi_store.table_preview_config.columns_map = value.reduce((acc, col, index) => {
      acc[col.key] = {
        ...col,
        order_index: index,
      };
      return acc;
    }, {});
    bi_store.is_table_dirty = true;
  },
});

function updateColumnVisibility(column) {
  bi_store.table_preview_config.columns_map[column.key].visible = !column.visible;
  bi_store.is_table_dirty = true;
}

function getFieldIconType(alias) {
  const field = bi_store.alias_to_column_mapping[alias];
  return field.agg ? 'function' : field.type;
}

function updateTableConfig(key, value) {
  bi_store.table_preview_config[key] = value;
  bi_store.is_table_dirty = true;
}
</script>

<template>
  <div>
    <div class="font-semibold mb-3 text-sm">
      {{ $t('Columns') }}
    </div>
    <draggable
      v-model="columns"
      :group="{ name: 'fields', pull: true, put: true }"
      item-key="key" handle=".move" class="min-h-[50px] gap-y-1 mb-6"
    >
      <template #item="{ element }">
        <div class="p-2 rounded-md group hover:bg-gray-50 my-2.5 border border-gray-200">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="pr-3 move cursor-move">
                <IconHawkDragIcon class="text-gray-400 w-[12px] h-[12px]" />
              </div>
              <div class="flex items-center">
                <div :class="getFieldIconType(element.key) === 'function' ? 'border-l-4 border-warning-300' : ''">
                  <component :is="getIconsForType(getFieldIconType(element.key))" class="text-gray-600 w-4 h-4" />
                </div>
                <span class="text-sm text-gray-600 pl-2">{{ element.label || element.key }}</span>
              </div>
            </div>
            <div class="cursor-pointer text-gray-600" @click.stop="updateColumnVisibility(element)">
              <IconHawkEyeTwo v-if="element.visible" class="h-4 w-4" />
              <IconHawkEyeOff v-else class="h-4 w-4" />
            </div>
          </div>
        </div>
      </template>
    </draggable>

    <div class="border-b border-gray-200 mb-3" />

    <Vueform size="sm" class="mt-6">
      <div class="col-span-12">
        <div class="flex items-start justify-between mb-4">
          <div>
            <div class="text-sm font-medium text-gray-700">
              {{ $t('Display row numbers') }}
            </div>
          </div>
          <ToggleElement
            name="show_row_headers"
            class="ml-2"
            :default="bi_store.table_preview_config.show_row_headers"
            @change="updateTableConfig('show_row_headers', $event)"
          />
        </div>
      </div>
    </Vueform>
  </div>
</template>
