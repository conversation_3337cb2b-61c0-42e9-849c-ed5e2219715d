<script setup>
import { storeToRefs } from 'pinia';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';
import { useBiStore } from '~/bi/store/bi.store';

const emit = defineEmits(['close']);

const bi_store = useBiStore();

const state = reactive({
  mode: 'data-builder',
});

function publishWidget() {
  logger.log('PUBLISH WIDGET');
}

const { stages, selected_table } = storeToRefs(bi_store);
const { createDefaultSelection } = useBIQueryBuilder();
onMounted(async () => {
  bi_store.resetStore();
  await bi_store.getTables();
  stages.value.push({
    selected_table: selected_table.value,
    value: createDefaultSelection(),
    tables: [selected_table.value],
  });
});
</script>

<template>
  <BiLayout @close="emit('close')">
    <template #left-content>
      <BiQueryBuilder v-if="state.mode === 'data-builder'" />
      <BiChartBuilder v-else-if="state.mode === 'chart-builder'" @go-back="state.mode = 'data-builder'" />
    </template>
    <template #right-content>
      <BiDataPreview
        v-if="state.mode === 'data-builder'"
        @continue="state.mode = 'chart-builder'"
      />
      <BiWidgetPreview
        v-else-if="state.mode === 'chart-builder'"
        @continue="publishWidget"
      />
    </template>
  </BiLayout>
</template>
