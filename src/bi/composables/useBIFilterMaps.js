// Operator components
import BetweenOperator from '~/bi/components/filters/operator-configs/between_operator.vue';
import CheckboxOperator from '~/bi/components/filters/operator-configs/checkbox_operator.vue';
import NoneOperator from '~/bi/components/filters/operator-configs/none_operator.vue';
import Single_input_operator from '~/bi/components/filters/operator-configs/single_input_operator.vue';
import SliderOperator from '~/bi/components/filters/operator-configs/slider_operator.vue';

export function useBIFilterMaps() {
  const operators_config_map = {
  // User-friendly comparison operators
    equals: {
      operator: 'equals',
      label: 'Equals',
      description: 'Value equals the specified value',
      supportedTypes: ['text', 'integer', 'float', 'boolean', 'date', 'timestamp'],
      valueType: 'single',
      valueRequired: true,
      examples: [
        { value: 'active', description: 'Status equals "active"' },
        { value: 100, description: 'Amount equals 100' },
        { value: '2024-01-01', description: 'Date equals January 1, 2024' },
      ],
    },

    does_not_equal: {
      operator: 'does_not_equal',
      label: 'Does Not Equal',
      description: 'Value does not equal the specified value',
      supportedTypes: ['text', 'integer', 'float', 'boolean', 'date', 'timestamp'],
      valueType: 'single',
      valueRequired: true,
      examples: [
        { value: 'inactive', description: 'Status does not equal "inactive"' },
        { value: 0, description: 'Amount does not equal 0' },
      ],
    },

    greater_than: {
      operator: 'greater_than',
      label: 'Greater Than',
      description: 'Value is greater than the specified number or date',
      supportedTypes: ['integer', 'float', 'date', 'timestamp'],
      valueType: 'single',
      valueRequired: true,
      examples: [
        { value: 100, description: 'Amount greater than 100' },
        { value: '2024-01-01', description: 'Date after January 1, 2024' },
      ],
    },

    greater_than_or_equal_to: {
      operator: 'greater_than_or_equal_to',
      label: 'Greater Than or Equal To',
      description: 'Value is greater than or equal to the specified number or date',
      supportedTypes: ['integer', 'float', 'date', 'timestamp'],
      valueType: 'single',
      valueRequired: true,
      examples: [
        { value: 100, description: 'Amount >= 100' },
        { value: '2024-01-01', description: 'Date on or after January 1, 2024' },
      ],
    },

    less_than: {
      operator: 'less_than',
      label: 'Less Than',
      description: 'Value is less than the specified number or date',
      supportedTypes: ['integer', 'float', 'date', 'timestamp'],
      valueType: 'single',
      valueRequired: true,
      examples: [
        { value: 1000, description: 'Amount less than 1000' },
        { value: '2024-12-31', description: 'Date before December 31, 2024' },
      ],
    },

    less_than_or_equal_to: {
      operator: 'less_than_or_equal_to',
      label: 'Less Than or Equal To',
      description: 'Value is less than or equal to the specified number or date',
      supportedTypes: ['integer', 'float', 'date', 'timestamp'],
      valueType: 'single',
      valueRequired: true,
      examples: [
        { value: 1000, description: 'Amount <= 1000' },
        { value: '2024-12-31', description: 'Date on or before December 31, 2024' },
      ],
    },

    // User-friendly text operators
    is: {
      operator: 'is',
      label: 'Is',
      description: 'Text or value is exactly the specified value',
      supportedTypes: ['text', 'integer', 'float', 'boolean', 'date', 'timestamp'],
      valueType: 'single',
      valueRequired: true,
      examples: [
        { value: 'completed', description: 'Status is "completed"' },
        { value: true, description: 'Is active is true' },
      ],
    },

    is_not: {
      operator: 'is_not',
      label: 'Is Not',
      description: 'Text or value is not the specified value',
      supportedTypes: ['text', 'integer', 'float', 'boolean', 'date', 'timestamp'],
      valueType: 'single',
      valueRequired: true,
      examples: [
        { value: 'draft', description: 'Status is not "draft"' },
        { value: false, description: 'Is deleted is not false' },
      ],
    },

    contains: {
      operator: 'contains',
      label: 'Contains',
      description: 'Text contains the specified substring (case-sensitive)',
      supportedTypes: ['text'],
      valueType: 'single',
      valueRequired: true,
      examples: [
        { value: 'john', description: 'Name contains "john"' },
        { value: 'Project', description: 'Description contains "Project"' },
      ],
    },

    does_not_contain: {
      operator: 'does_not_contain',
      label: 'Does Not Contain',
      description: 'Text does not contain the specified substring (case-sensitive)',
      supportedTypes: ['text'],
      valueType: 'single',
      valueRequired: true,
      examples: [
        { value: 'test', description: 'Name does not contain "test"' },
        { value: 'draft', description: 'Status does not contain "draft"' },
      ],
    },

    starts_with: {
      operator: 'starts_with',
      label: 'Starts With',
      description: 'Text starts with the specified string',
      supportedTypes: ['text'],
      valueType: 'single',
      valueRequired: true,
      examples: [
        { value: 'Mr', description: 'Name starts with "Mr"' },
        { value: 'Project', description: 'Title starts with "Project"' },
      ],
    },

    ends_with: {
      operator: 'ends_with',
      label: 'Ends With',
      description: 'Text ends with the specified string',
      supportedTypes: ['text'],
      valueType: 'single',
      valueRequired: true,
      examples: [
        { value: 'Jr', description: 'Name ends with "Jr"' },
        { value: '.pdf', description: 'Filename ends with ".pdf"' },
      ],
    },

    // Empty/Null check operators
    is_empty: {
      operator: 'is_empty',
      label: 'Is Empty',
      description: 'Field is null or empty (for text fields, checks both null and empty string)',
      supportedTypes: ['text', 'integer', 'float', 'boolean', 'date', 'timestamp'],
      valueType: 'none',
      valueRequired: false,
      examples: [
        { value: undefined, description: 'Description is empty' },
        { value: undefined, description: 'Optional field is empty' },
      ],
    },

    is_not_empty: {
      operator: 'is_not_empty',
      label: 'Is Not Empty',
      description: 'Field has a value (for text fields, not null and not empty string)',
      supportedTypes: ['text', 'integer', 'float', 'boolean', 'date', 'timestamp'],
      valueType: 'none',
      valueRequired: false,
      examples: [
        { value: undefined, description: 'Description is not empty' },
        { value: undefined, description: 'Required field has a value' },
      ],
    },

    // Range and list operators
    between: {
      operator: 'between',
      label: 'Between',
      description: 'Value is between two numbers or dates (inclusive)',
      supportedTypes: ['integer', 'float', 'date', 'timestamp'],
      valueType: 'range',
      valueRequired: true,
      examples: [
        { value: [100, 500], description: 'Amount between 100 and 500' },
        { value: ['2024-01-01', '2024-12-31'], description: 'Date in 2024' },
      ],
    },

    not_between: {
      operator: 'not_between',
      label: 'Not Between',
      description: 'Value is not between two numbers or dates',
      supportedTypes: ['integer', 'float', 'date', 'timestamp'],
      valueType: 'range',
      valueRequired: true,
      examples: [
        { value: [100, 500], description: 'Amount not between 100 and 500' },
        { value: ['2024-01-01', '2024-12-31'], description: 'Date not in 2024' },
      ],
    },

    in: {
      operator: 'in',
      label: 'In List',
      description: 'Value matches any item in the specified list',
      supportedTypes: ['text', 'integer', 'float', 'date', 'timestamp'],
      valueType: 'array',
      valueRequired: true,
      examples: [
        { value: ['active', 'pending'], description: 'Status is active or pending' },
        { value: [1, 2, 3], description: 'Category ID is 1, 2, or 3' },
      ],
    },

    not_in: {
      operator: 'not_in',
      label: 'Not In List',
      description: 'Value does not match any item in the specified list',
      supportedTypes: ['text', 'integer', 'float', 'date', 'timestamp'],
      valueType: 'array',
      valueRequired: true,
      examples: [
        { value: ['draft', 'archived'], description: 'Status is not draft or archived' },
        { value: [0, -1], description: 'Amount is not 0 or -1' },
      ],
    },

    // Date operators
    relative_date_current: {
      operator: 'relative_date_current',
      label: 'Current Period',
      description: 'Date falls within current time period',
      supportedTypes: ['date', 'timestamp'],
      valueType: 'date_period',
      valueRequired: true,
      examples: [
        { value: 'week', description: 'This week (Monday to Sunday)' },
        { value: 'month', description: 'This month (1st to last day)' },
        { value: 'quarter', description: 'This quarter (Q1, Q2, Q3, or Q4)' },
        { value: 'year', description: 'This year (Jan 1 to Dec 31)' },
      ],
    },

    relative_date_last: {
      operator: 'relative_date_last',
      label: 'Last Period',
      description: 'Date falls within last time period (with optional anchor date)',
      supportedTypes: ['date', 'timestamp'],
      valueType: 'date_anchor',
      valueRequired: true,
      examples: [
        { value: '7_days', description: 'Last 7 days from today' },
        { value: 'week', description: 'Last complete week' },
        { value: ['30_days', '2024-07-15'], description: 'Last 30 days from July 15, 2024' },
      ],
    },

    relative_date_next: {
      operator: 'relative_date_next',
      label: 'Next Period',
      description: 'Date falls within next time period (with optional anchor date)',
      supportedTypes: ['date', 'timestamp'],
      valueType: 'date_anchor',
      valueRequired: true,
      examples: [
        { value: 'week', description: 'Next complete week' },
        { value: 'month', description: 'Next complete month' },
        { value: ['2_weeks', '2024-07-15'], description: 'Next 2 weeks from July 15, 2024' },
      ],
    },

    week_to_date: {
      operator: 'week_to_date',
      label: 'Week to Date',
      description: 'From Monday of current week to today (or anchor date)',
      supportedTypes: ['date', 'timestamp'],
      valueType: 'single',
      valueRequired: false,
      examples: [
        { value: undefined, description: 'Monday to today' },
        { value: '2024-07-15', description: 'Monday of July 15 week to July 15' },
      ],
    },

    month_to_date: {
      operator: 'month_to_date',
      label: 'Month to Date',
      description: 'From 1st of current month to today (or anchor date)',
      supportedTypes: ['date', 'timestamp'],
      valueType: 'single',
      valueRequired: false,
      examples: [
        { value: undefined, description: '1st of month to today' },
        { value: '2024-07-15', description: 'July 1 to July 15, 2024' },
      ],
    },

    quarter_to_date: {
      operator: 'quarter_to_date',
      label: 'Quarter to Date',
      description: 'From start of current quarter to today (or anchor date)',
      supportedTypes: ['date', 'timestamp'],
      valueType: 'single',
      valueRequired: false,
      examples: [
        { value: undefined, description: 'Quarter start to today' },
        { value: '2024-07-15', description: 'July 1 to July 15, 2024 (Q3)' },
      ],
    },

    year_to_date: {
      operator: 'year_to_date',
      label: 'Year to Date',
      description: 'From January 1st of current year to today (or anchor date)',
      supportedTypes: ['date', 'timestamp'],
      valueType: 'single',
      valueRequired: false,
      examples: [
        { value: undefined, description: 'Jan 1 to today' },
        { value: '2024-07-15', description: 'Jan 1 to July 15, 2024' },
      ],
    },

    // Boolean operators
    is_true: {
      operator: 'is_true',
      label: 'Is True',
      description: 'Boolean field is true',
      supportedTypes: ['boolean'],
      valueType: 'none',
      valueRequired: false,
      examples: [
        { value: undefined, description: 'Is active is true' },
      ],
    },

    is_false: {
      operator: 'is_false',
      label: 'Is False',
      description: 'Boolean field is false',
      supportedTypes: ['boolean'],
      valueType: 'none',
      valueRequired: false,
      examples: [
        { value: undefined, description: 'Is deleted is false' },
      ],
    },
  };

  // const component_map = {
  //   checkbox: CheckboxOperator,
  //   input: Single_input_operator,
  //   between: BetweenOperator,
  //   slider: SliderOperator,
  //   none: NoneOperator,
  // // 'text-element': TextInputComponent,
  // // 'autocomplete-element': AutocompleteInputComponent,
  // // 'radio-group-element': RadioGroupComponent,
  // // 'radio-group-element-with-blank-not-blank': RadioGroupComponent,
  // // 'number-element': TextInputComponent,
  // // 'number-element-between': NumberRangeComponent,
  // // 'multi-select-input': MultiSelectComponent,
  // // 'single-value-select': MultiSelectComponent,
  // // 'date-input': DateInputComponent
  // };

  // Contains mapping of value types and field types to components and their configurations
  const value_type_map_field_component = {
  // if value type is none send Single_input_operator as text input,
    single: {
      text: {
        component: Single_input_operator,
        componentConfig: {
          inputType: 'text',
        },
        columnConfig: {},
      },
      integer: {
        component: Single_input_operator,
        componentConfig: {
          inputType: 'number',
        },
        columnConfig: {},
      },
      float: {
        component: Single_input_operator,
        componentConfig: {
          inputType: 'number',
        },
        columnConfig: {},
      },
    },
    array: {
      text: {
        component: CheckboxOperator,
        componentConfig: {},
        columnConfig: {},
      },
      number: {
        component: CheckboxOperator,
        componentConfig: {},
        columnConfig: {},
      },
    },
    range: {
      integer: {
        component: BetweenOperator,
        componentConfig: {},
        columnConfig: {},
      },
      float: {
        component: SliderOperator,
        componentConfig: {},
        columnConfig: {},
      },
    },
  };

  function getTypeOperatorMap() {
    return Object.entries(operators_config_map).reduce((acc, [key, config]) => {
      config.supportedTypes.forEach((type) => {
        if (!acc[type]) {
          acc[type] = [];
        }
        acc[type].push(config.operator);
      });
      return acc;
    }, {});
  }

  function getOperatorComponent({ selected_operator, selected_column_config, column_options }) {
    // console.log('Get Operator Component:', selected_operator, selected_column_config, column_options);
    // console.log(selected_operator?.valueType === 'none', selected_operator?.valueType);
    if (selected_operator?.valueType === 'none') {
      return {
        component: NoneOperator,
        componentConfig: {},
        columnConfig: {},
      };
    }
    else {
      const component_data = value_type_map_field_component[selected_operator?.valueType]?.[selected_column_config.type];

      if (selected_operator?.valueType === 'array') {
        component_data.columnConfig.options = column_options;
      }

      return value_type_map_field_component[selected_operator?.valueType]?.[selected_column_config.type];
    }
  }

  return {
    operators_config_map,
    getTypeOperatorMap,
    getOperatorComponent,
  };
}
